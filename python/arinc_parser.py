#!/usr/bin/env python3
"""
Python wrapper for ARINC 424 parsing to be used by the C++ application.
This script provides a simplified interface to the arinc424 library.
"""

import sys
import os
import json

# Add the src directory to the path to import arinc424
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(os.path.dirname(current_dir), 'src')
sys.path.insert(0, src_dir)

try:
    import arinc424
except ImportError as e:
    print(f"Error importing arinc424: {e}", file=sys.stderr)
    sys.exit(1)

def parse_record(record_line):
    """
    Parse a single ARINC 424 record line and return JSON data.
    
    Args:
        record_line (str): The ARINC 424 record line
        
    Returns:
        dict: Parsed record data or None if parsing failed
    """
    try:
        record = arinc424.Record()
        if record.read(record_line.strip()):
            # Get JSON representation
            json_str = record.json(output=False)
            return json.loads(json_str)
        else:
            return None
    except Exception as e:
        print(f"Error parsing record: {e}", file=sys.stderr)
        return None

def parse_file(file_path):
    """
    Parse an entire ARINC 424 file and return all records as JSON.
    
    Args:
        file_path (str): Path to the ARINC 424 file
        
    Returns:
        list: List of parsed records
    """
    records = []
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            line_count = 0
            for line in f:
                line_count += 1
                
                # Skip header lines
                if line.startswith('HDR'):
                    continue
                
                record_data = parse_record(line)
                if record_data:
                    records.append(record_data)
                
                # Progress feedback for large files
                if line_count % 1000 == 0:
                    print(f"Processed {line_count} lines, parsed {len(records)} records", file=sys.stderr)
                    
    except Exception as e:
        print(f"Error reading file {file_path}: {e}", file=sys.stderr)
        return []
    
    return records

def get_record_types():
    """
    Get available record types from the arinc424 library.
    
    Returns:
        dict: Mapping of section codes to record type names
    """
    # This would ideally come from the library, but we'll hardcode the known types
    return {
        'D ': 'VHF Navaid',
        'DB': 'NDB Navaid', 
        'EA': 'Waypoint',
        'EM': 'Airways Marker',
        'EP': 'Holding Pattern',
        'ER': 'Enroute Airways',
        'ET': 'Preferred Route',
        'PA': 'Airport',
        'PB': 'Airport Gate',
        'PC': 'Terminal Waypoint',
        'PD': 'SID',
        'PE': 'STAR', 
        'PF': 'Approach',
        'PG': 'Runway',
        'PI': 'Localizer/Glideslope',
        'PK': 'TAA',
        'PL': 'MLS',
        'PM': 'Localizer Marker',
        'PN': 'NDB Navaid',
        'PP': 'Path Point',
        'PR': 'Flight Planning',
        'PS': 'MSA',
        'PT': 'GLS',
        'PV': 'Airport Communication',
        'R ': 'Company Route',
        'RA': 'Alternate',
        'TC': 'Cruising Tables',
        'TG': 'Geo Reference Table',
        'UC': 'Controlled Airspace',
        'UF': 'FIR/UIR',
        'UR': 'Restrictive Airspace'
    }

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python arinc_parser.py <command> [args...]")
        print("Commands:")
        print("  parse_file <file_path>    - Parse entire file and output JSON")
        print("  parse_record <record>     - Parse single record and output JSON")
        print("  get_types                 - Get available record types")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "parse_file":
        if len(sys.argv) < 3:
            print("Error: file_path required", file=sys.stderr)
            sys.exit(1)
        
        file_path = sys.argv[2]
        records = parse_file(file_path)
        print(json.dumps(records, indent=2))
        
    elif command == "parse_record":
        if len(sys.argv) < 3:
            print("Error: record line required", file=sys.stderr)
            sys.exit(1)
        
        record_line = sys.argv[2]
        record_data = parse_record(record_line)
        if record_data:
            print(json.dumps(record_data, indent=2))
        else:
            print("Failed to parse record", file=sys.stderr)
            sys.exit(1)
            
    elif command == "get_types":
        types = get_record_types()
        print(json.dumps(types, indent=2))
        
    else:
        print(f"Unknown command: {command}", file=sys.stderr)
        sys.exit(1)
