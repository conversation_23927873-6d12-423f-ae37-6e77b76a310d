#pragma once

// Prevent Qt's slots macro from conflicting with Python headers
#ifdef slots
#undef slots
#endif
#include <Python.h>
#define slots Q_SLOTS
#include <QString>
#include <QVector>
#include <QVariantMap>
#include <QGeoCoordinate>
#include "DataModels.h"

class PythonInterface {
public:
    PythonInterface();
    ~PythonInterface();
    
    bool initialize();
    void cleanup();
    
    // Parse ARINC 424 file using Python library
    bool parseArincFile(const QString& filePath, ArincDataModel& dataModel);
    
    // Parse individual record
    ArincRecord parseArincRecord(const QString& recordLine);
    
private:
    bool m_initialized;
    PyObject* m_arincModule;
    PyObject* m_recordClass;
    
    // Helper methods
    bool initializePython();
    bool loadArincModule();
    QGeoCoordinate parseCoordinate(const QString& latStr, const QString& lonStr);
    QVariantMap pythonDictToQVariantMap(PyObject* dict);
    QString pythonStringToQString(PyObject* pyStr);
    double parseLatitude(const QString& latStr);
    double parseLongitude(const QString& lonStr);
};
