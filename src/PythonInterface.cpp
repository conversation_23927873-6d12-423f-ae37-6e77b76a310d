#include "PythonInterface.h"
#include <QDebug>
#include <QDir>
#include <QCoreApplication>
#include <QJsonDocument>
#include <QJsonObject>
#include <QRegularExpression>

PythonInterface::PythonInterface() 
    : m_initialized(false), m_arincModule(nullptr), m_recordClass(nullptr) {
}

PythonInterface::~PythonInterface() {
    cleanup();
}

bool PythonInterface::initialize() {
    if (m_initialized) return true;
    
    if (!initializePython()) {
        qDebug() << "Failed to initialize Python";
        return false;
    }
    
    if (!loadArincModule()) {
        qDebug() << "Failed to load ARINC module";
        return false;
    }
    
    m_initialized = true;
    return true;
}

void PythonInterface::cleanup() {
    if (m_recordClass) {
        Py_DECREF(m_recordClass);
        m_recordClass = nullptr;
    }
    if (m_arincModule) {
        Py_DECREF(m_arincModule);
        m_arincModule = nullptr;
    }
    if (m_initialized) {
        Py_Finalize();
        m_initialized = false;
    }
}

bool PythonInterface::initializePython() {
    Py_Initialize();
    if (!Py_IsInitialized()) {
        return false;
    }
    
    // Add current directory and src directory to Python path
    QString appDir = QCoreApplication::applicationDirPath();
    QString srcDir = QDir(appDir).absoluteFilePath("../src");
    
    PyRun_SimpleString("import sys");
    PyRun_SimpleString(QString("sys.path.append('%1')").arg(appDir).toUtf8().data());
    PyRun_SimpleString(QString("sys.path.append('%1')").arg(srcDir).toUtf8().data());
    
    return true;
}

bool PythonInterface::loadArincModule() {
    // Import the arinc424 module
    m_arincModule = PyImport_ImportModule("arinc424");
    if (!m_arincModule) {
        PyErr_Print();
        return false;
    }
    
    // Get the Record class
    m_recordClass = PyObject_GetAttrString(m_arincModule, "Record");
    if (!m_recordClass) {
        PyErr_Print();
        return false;
    }
    
    return true;
}

bool PythonInterface::parseArincFile(const QString& filePath, ArincDataModel& dataModel) {
    if (!m_initialized) {
        qDebug() << "Python interface not initialized";
        return false;
    }
    
    dataModel.clearData();
    
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qDebug() << "Cannot open file:" << filePath;
        return false;
    }
    
    QTextStream in(&file);
    int lineCount = 0;
    int parsedCount = 0;
    
    while (!in.atEnd()) {
        QString line = in.readLine();
        lineCount++;
        
        // Skip header lines
        if (line.startsWith("HDR")) {
            continue;
        }
        
        ArincRecord record = parseArincRecord(line);
        if (record.isValid()) {
            dataModel.addRecord(record);
            parsedCount++;
        }
        
        // Progress feedback for large files
        if (lineCount % 1000 == 0) {
            qDebug() << "Processed" << lineCount << "lines, parsed" << parsedCount << "records";
        }
    }
    
    qDebug() << "Finished parsing. Total lines:" << lineCount << "Parsed records:" << parsedCount;
    return true;
}

ArincRecord PythonInterface::parseArincRecord(const QString& recordLine) {
    ArincRecord record;
    
    if (!m_initialized || !m_recordClass) {
        return record;
    }
    
    // Create a new Record instance
    PyObject* recordInstance = PyObject_CallObject(m_recordClass, nullptr);
    if (!recordInstance) {
        PyErr_Print();
        return record;
    }
    
    // Call the read method
    PyObject* readMethod = PyObject_GetAttrString(recordInstance, "read");
    if (!readMethod) {
        Py_DECREF(recordInstance);
        return record;
    }
    
    PyObject* args = PyTuple_New(1);
    PyTuple_SetItem(args, 0, PyUnicode_FromString(recordLine.toUtf8().data()));
    
    PyObject* readResult = PyObject_CallObject(readMethod, args);
    Py_DECREF(args);
    Py_DECREF(readMethod);
    
    if (!readResult || !PyBool_Check(readResult) || !PyObject_IsTrue(readResult)) {
        Py_XDECREF(readResult);
        Py_DECREF(recordInstance);
        return record; // Failed to parse
    }
    Py_DECREF(readResult);
    
    // Get JSON representation
    PyObject* jsonMethod = PyObject_GetAttrString(recordInstance, "json");
    if (!jsonMethod) {
        Py_DECREF(recordInstance);
        return record;
    }
    
    // Call json method with output=False
    PyObject* jsonArgs = PyTuple_New(1);
    PyTuple_SetItem(jsonArgs, 0, Py_False);
    Py_INCREF(Py_False);
    
    PyObject* jsonResult = PyObject_CallObject(jsonMethod, jsonArgs);
    Py_DECREF(jsonArgs);
    Py_DECREF(jsonMethod);
    
    if (!jsonResult) {
        Py_DECREF(recordInstance);
        return record;
    }
    
    QString jsonString = pythonStringToQString(jsonResult);
    Py_DECREF(jsonResult);
    Py_DECREF(recordInstance);
    
    // Parse JSON to extract record data
    QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8());
    if (!doc.isObject()) {
        return record;
    }
    
    QJsonObject obj = doc.object();
    
    // Extract basic information
    record.sectionCode = obj["Section Code"].toString();
    record.customerAreaCode = obj["Customer / Area Code"].toString();

    // Determine record type - handle special case for SID/STAR/Approach procedures
    // These might have section code "PA" but are actually procedures
    record.type = parseRecordType(record.sectionCode);

    // Check if this is actually a SID/STAR/Approach procedure
    if (record.sectionCode == "PA" && obj.contains("SID/STAR/Approach Identifier")) {
        QString procedureId = obj["SID/STAR/Approach Identifier"].toString().trimmed();
        QString routeType = obj.value("Route Type").toString();

        if (!procedureId.isEmpty()) {
            // Determine procedure type based on route type or other indicators
            if (routeType == "1" || routeType == "2" || routeType == "3") {
                record.type = RecordType::STAR;  // STAR route types
            } else if (routeType == "0" || routeType == "1" || routeType == "2" || routeType == "3") {
                record.type = RecordType::SID;   // SID route types (overlapping, need better logic)
            } else {
                record.type = RecordType::APPROACH;
            }
            record.procedureIdentifier = procedureId;
            record.routeType = routeType;
        }
    }
    
    // Extract identifier and name
    if (obj.contains("VOR Identifier")) {
        record.identifier = obj["VOR Identifier"].toString().trimmed();
    } else if (obj.contains("Waypoint Identifier")) {
        record.identifier = obj["Waypoint Identifier"].toString().trimmed();
    } else if (obj.contains("Airport Identifier")) {
        record.identifier = obj["Airport Identifier"].toString().trimmed();
    } else if (obj.contains("Fix Identifier")) {
        record.identifier = obj["Fix Identifier"].toString().trimmed();
    }

    // Extract sequence number for procedures
    if (obj.contains("Sequence Number")) {
        QString seqStr = obj["Sequence Number"].toString().trimmed();
        bool ok;
        int seq = seqStr.toInt(&ok);
        if (ok) {
            record.sequenceNumber = seq;
        }
    }
    
    if (obj.contains("VOR Name")) {
        record.name = obj["VOR Name"].toString().trimmed();
    } else if (obj.contains("Airport Name")) {
        record.name = obj["Airport Name"].toString().trimmed();
    }
    
    // Extract coordinates
    QString latStr, lonStr;
    if (obj.contains("DME Latitude") && obj.contains("DME Longitude")) {
        latStr = obj["DME Latitude"].toString();
        lonStr = obj["DME Longitude"].toString();
    } else if (obj.contains("VOR Latitude") && obj.contains("VOR Longitude")) {
        latStr = obj["VOR Latitude"].toString();
        lonStr = obj["VOR Longitude"].toString();
    } else if (obj.contains("Waypoint Latitude") && obj.contains("Waypoint Longitude")) {
        latStr = obj["Waypoint Latitude"].toString();
        lonStr = obj["Waypoint Longitude"].toString();
    }
    
    if (!latStr.isEmpty() && !lonStr.isEmpty()) {
        record.coordinate = parseCoordinate(latStr, lonStr);
    }
    
    // Store all additional data
    for (auto it = obj.begin(); it != obj.end(); ++it) {
        record.additionalData[it.key()] = it.value().toVariant();
    }
    
    return record;
}

QGeoCoordinate PythonInterface::parseCoordinate(const QString& latStr, const QString& lonStr) {
    if (latStr.isEmpty() || lonStr.isEmpty() || latStr.trimmed().isEmpty() || lonStr.trimmed().isEmpty()) {
        return QGeoCoordinate();
    }

    double lat = parseLatitude(latStr);
    double lon = parseLongitude(lonStr);

    if (qIsNaN(lat) || qIsNaN(lon)) {
        return QGeoCoordinate();
    }

    return QGeoCoordinate(lat, lon);
}

double PythonInterface::parseLatitude(const QString& latStr) {
    // Format: N47265700 or S47265700 (degrees, minutes, seconds * 100)
    if (latStr.length() < 9) return qQNaN();

    QChar hemisphere = latStr[0];
    QString coords = latStr.mid(1);

    if (coords.length() != 8) return qQNaN();

    bool ok;
    int degrees = coords.mid(0, 2).toInt(&ok);
    if (!ok) return qQNaN();

    int minutes = coords.mid(2, 2).toInt(&ok);
    if (!ok) return qQNaN();

    int seconds = coords.mid(4, 4).toInt(&ok);
    if (!ok) return qQNaN();

    double lat = degrees + minutes / 60.0 + (seconds / 100.0) / 3600.0;

    if (hemisphere == 'S') lat = -lat;

    return lat;
}

double PythonInterface::parseLongitude(const QString& lonStr) {
    // Format: W122182910 or E122182910 (degrees, minutes, seconds * 100)
    if (lonStr.length() < 10) return qQNaN();

    QChar hemisphere = lonStr[0];
    QString coords = lonStr.mid(1);

    if (coords.length() != 9) return qQNaN();

    bool ok;
    int degrees = coords.mid(0, 3).toInt(&ok);
    if (!ok) return qQNaN();

    int minutes = coords.mid(3, 2).toInt(&ok);
    if (!ok) return qQNaN();

    int seconds = coords.mid(5, 4).toInt(&ok);
    if (!ok) return qQNaN();

    double lon = degrees + minutes / 60.0 + (seconds / 100.0) / 3600.0;

    if (hemisphere == 'W') lon = -lon;

    return lon;
}

QString PythonInterface::pythonStringToQString(PyObject* pyStr) {
    if (!pyStr || !PyUnicode_Check(pyStr)) {
        return QString();
    }

    const char* utf8 = PyUnicode_AsUTF8(pyStr);
    if (!utf8) {
        return QString();
    }

    return QString::fromUtf8(utf8);
}
