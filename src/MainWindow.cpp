#include "MainWindow.h"
#include <QApplication>
#include <QMessageBox>
#include <QThread>
#include <QDebug>

MainWindow::MainWindow(QWidget* parent) : QMainWindow(parent) {
    m_dataModel = new ArincDataModel(this);
    m_pythonInterface = new PythonInterface();
    m_updateTimer = new QTimer(this);
    m_updateTimer->setSingleShot(true);
    m_updateTimer->setInterval(500); // 500ms delay for filter updates
    
    setupUI();
    setupMenuBar();
    setupStatusBar();
    connectSignals();
    
    // Initialize Python interface
    if (!m_pythonInterface->initialize()) {
        QMessageBox::critical(this, "Error", 
            "Failed to initialize Python interface. Make sure Python 3 and the ARINC 424 library are installed.");
    }
    
    setWindowTitle("ARINC 424 Viewer");
    resize(1200, 800);
}

MainWindow::~MainWindow() {
    delete m_pythonInterface;
}

void MainWindow::setupUI() {
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);
    
    // Main splitter
    m_mainSplitter = new QSplitter(Qt::Horizontal, m_centralWidget);
    
    // Left panel for controls
    m_leftPanel = new QWidget();
    m_leftPanel->setMaximumWidth(300);
    m_leftPanel->setMinimumWidth(250);
    m_leftLayout = new QVBoxLayout(m_leftPanel);
    
    // Open file button
    m_openFileBtn = new QPushButton("Open ARINC 424 File", m_leftPanel);
    m_openFileBtn->setStyleSheet("QPushButton { font-weight: bold; padding: 10px; }");
    m_leftLayout->addWidget(m_openFileBtn);
    
    // Filter widget
    m_filterWidget = new FilterWidget(m_leftPanel);
    m_leftLayout->addWidget(m_filterWidget);
    
    m_leftPanel->setLayout(m_leftLayout);
    m_mainSplitter->addWidget(m_leftPanel);
    
    // Map widget
    m_mapWidget = new MapWidget();
    m_mainSplitter->addWidget(m_mapWidget);
    
    // Set splitter proportions
    m_mainSplitter->setStretchFactor(0, 0);
    m_mainSplitter->setStretchFactor(1, 1);
    
    // Main layout
    QHBoxLayout* mainLayout = new QHBoxLayout(m_centralWidget);
    mainLayout->addWidget(m_mainSplitter);
    m_centralWidget->setLayout(mainLayout);
    
    // Initialize map
    m_mapWidget->initializeMap();
}

void MainWindow::setupMenuBar() {
    QMenuBar* menuBar = this->menuBar();
    
    // File menu
    QMenu* fileMenu = menuBar->addMenu("&File");
    
    QAction* openAction = fileMenu->addAction("&Open ARINC 424 File...");
    openAction->setShortcut(QKeySequence::Open);
    connect(openAction, &QAction::triggered, this, &MainWindow::openFile);
    
    fileMenu->addSeparator();
    
    QAction* exitAction = fileMenu->addAction("E&xit");
    exitAction->setShortcut(QKeySequence::Quit);
    connect(exitAction, &QAction::triggered, this, &QWidget::close);
    
    // Help menu
    QMenu* helpMenu = menuBar->addMenu("&Help");
    
    QAction* aboutAction = helpMenu->addAction("&About");
    connect(aboutAction, &QAction::triggered, this, &MainWindow::showAbout);
}

void MainWindow::setupStatusBar() {
    QStatusBar* statusBar = this->statusBar();
    
    m_statusLabel = new QLabel("Ready");
    m_recordCountLabel = new QLabel("No data loaded");
    m_progressBar = new QProgressBar();
    m_progressBar->setVisible(false);
    
    statusBar->addWidget(m_statusLabel);
    statusBar->addPermanentWidget(m_recordCountLabel);
    statusBar->addPermanentWidget(m_progressBar);
}

void MainWindow::connectSignals() {
    connect(m_openFileBtn, &QPushButton::clicked, this, &MainWindow::openFile);
    connect(m_filterWidget, &FilterWidget::filtersChanged, this, &MainWindow::onFiltersChanged);
    connect(m_updateTimer, &QTimer::timeout, this, &MainWindow::updateMapDisplay);
}

void MainWindow::openFile() {
    QString fileName = QFileDialog::getOpenFileName(this,
        "Open ARINC 424 File", 
        m_currentFilePath.isEmpty() ? QDir::homePath() : QFileInfo(m_currentFilePath).absolutePath(),
        "ARINC 424 Files (*.pc *.txt);;All Files (*)");
    
    if (fileName.isEmpty()) return;
    
    m_currentFilePath = fileName;
    m_statusLabel->setText("Loading file...");
    m_progressBar->setVisible(true);
    m_progressBar->setRange(0, 0); // Indeterminate progress
    
    // Disable UI during loading
    m_openFileBtn->setEnabled(false);
    m_filterWidget->setEnabled(false);
    
    QApplication::processEvents();
    
    // Parse file in main thread (for simplicity)
    // In a production app, this should be done in a worker thread
    bool success = m_pythonInterface->parseArincFile(fileName, *m_dataModel);
    
    m_progressBar->setVisible(false);
    m_openFileBtn->setEnabled(true);
    m_filterWidget->setEnabled(true);
    
    if (success) {
        m_statusLabel->setText("File loaded successfully");
        updateFilterOptions();
        updateMapDisplay();
        updateStatusBar();
    } else {
        m_statusLabel->setText("Failed to load file");
        QMessageBox::warning(this, "Error", "Failed to parse the ARINC 424 file. Please check the file format.");
    }
}

void MainWindow::onFiltersChanged() {
    // Use timer to avoid too frequent updates while user is changing filters
    m_updateTimer->start();
}

void MainWindow::updateMapDisplay() {
    QStringList selectedRegions = m_filterWidget->getSelectedRegions();
    QStringList selectedTypes = m_filterWidget->getSelectedRecordTypes();
    QStringList selectedAirports = m_filterWidget->getSelectedAirports();
    
    // Get filtered records
    QVector<ArincRecord> filteredRecords = m_dataModel->getFilteredRecords(
        selectedRegions, selectedTypes, selectedAirports);
    
    // Get filtered procedures
    QVector<ProcedurePath> filteredProcedures = m_dataModel->getFilteredProcedures(
        selectedAirports, selectedTypes);
    
    // Update map
    m_mapWidget->plotRecords(filteredRecords);
    m_mapWidget->plotProcedures(filteredProcedures);
    
    // Update status
    m_statusLabel->setText(QString("Displaying %1 records").arg(filteredRecords.size()));
}

void MainWindow::updateFilterOptions() {
    m_filterWidget->setAvailableRegions(m_dataModel->getAvailableRegions());
    m_filterWidget->setAvailableRecordTypes(m_dataModel->getAvailableRecordTypes());
    m_filterWidget->setAvailableAirports(m_dataModel->getAvailableAirports());
}

void MainWindow::updateStatusBar() {
    int totalRecords = m_dataModel->getTotalRecordCount();
    m_recordCountLabel->setText(QString("Total records: %1").arg(totalRecords));
}

void MainWindow::showAbout() {
    QMessageBox::about(this, "About ARINC 424 Viewer",
        "ARINC 424 Viewer\n\n"
        "A C++ application for visualizing ARINC 424 aviation navigation data.\n\n"
        "Features:\n"
        "• Parse ARINC 424 files using Python library\n"
        "• Interactive map visualization\n"
        "• Filter by region, record type, and airport\n"
        "• Display navigation aids, waypoints, and procedures\n\n"
        "Built with Qt and Leaflet mapping.");
}
