#pragma once

#include <QWidget>
#include <QWebEngineView>
#include <QVBoxLayout>
#include <QGeoCoordinate>
#include <QVector>
#include "DataModels.h"

class MapWidget : public QWidget {
    Q_OBJECT

public:
    explicit MapWidget(QWidget* parent = nullptr);
    
    void initializeMap();
    void clearMap();
    void plotRecords(const QVector<ArincRecord>& records);
    void plotProcedures(const QVector<ProcedurePath>& procedures);
    void setMapCenter(const QGeoCoordinate& center, int zoom = 8);

public slots:
    void onRecordsUpdated(const QVector<ArincRecord>& records);
    void onProceduresUpdated(const QVector<ProcedurePath>& procedures);

private slots:
    void onMapReady();

private:
    void setupUI();
    void createMapHTML();
    QString generateMapHTML();
    QString recordToGeoJSON(const ArincRecord& record);
    QString procedureToGeoJSON(const ProcedurePath& procedure);
    QString getRecordColor(RecordType type);
    QString getRecordIcon(RecordType type);
    
    QVBoxLayout* m_layout;
    QWebEngineView* m_webView;
    bool m_mapReady;
    
    QVector<ArincRecord> m_currentRecords;
    QVector<ProcedurePath> m_currentProcedures;
};
