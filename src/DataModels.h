#pragma once

#include <QString>
#include <QVector>
#include <QVariantMap>
#include <QGeoCoordinate>

enum class RecordType {
    VHF_NAVAID,      // D
    NDB_NAVAID,      // DB
    WAYPOINT,        // EA
    AIRWAYS_MARKER,  // EM
    HOLDING_PATTERN, // EP
    ENROUTE_AIRWAYS, // ER
    PREFERRED_ROUTE, // ET
    AIRPORT,         // PA
    AIRPORT_NDB,     // PN
    SID,             // PD (or PA with route type indicating SID)
    STAR,            // PE (or PA with route type indicating STAR)
    APPROACH,        // PF (or PA with route type indicating Approach)
    CONTROLLED_AIRSPACE, // UC
    RESTRICTIVE_AIRSPACE, // UR
    UNKNOWN
};

struct ArincRecord {
    RecordType type;
    QString sectionCode;
    QString customerAreaCode;  // Region (CAN, USA, etc.)
    QString identifier;
    QString name;
    QGeoCoordinate coordinate;
    QVariantMap additionalData;
    
    // For approaches/departures - sequence information
    int sequenceNumber = -1;
    QString procedureIdentifier;
    QString routeType;
    
    // Constructor
    ArincRecord() : type(RecordType::UNKNOWN) {}
    
    bool isValid() const {
        return type != RecordType::UNKNOWN && coordinate.isValid();
    }
    
    QString getDisplayName() const {
        if (!name.isEmpty()) return name;
        if (!identifier.isEmpty()) return identifier;
        return "Unknown";
    }
};

struct ProcedurePath {
    QString procedureId;
    QString airportCode;
    RecordType procedureType;
    QVector<ArincRecord> waypoints;
    
    bool isValid() const {
        return !procedureId.isEmpty() && waypoints.size() > 1;
    }
};

class ArincDataModel {
public:
    ArincDataModel();
    
    // Data management
    void addRecord(const ArincRecord& record);
    void clearData();
    void finalizeProcedures(); // Call after all records are loaded
    
    // Filtering
    QVector<ArincRecord> getFilteredRecords(
        const QStringList& regions = {},
        const QStringList& recordTypes = {},
        const QStringList& airports = {}
    ) const;
    
    QVector<ProcedurePath> getFilteredProcedures(
        const QStringList& airports = {},
        const QStringList& procedureTypes = {}
    ) const;
    
    // Getters for filter options (hierarchical)
    QStringList getAvailableRegions() const;
    QStringList getAvailableAirports(const QStringList& selectedRegions = {}) const;
    QStringList getAvailableRecordTypes(const QStringList& selectedAirports = {}) const;
    
    // Statistics
    int getTotalRecordCount() const { return m_records.size(); }
    int getRecordCount(RecordType type) const;
    
private:
    QVector<ArincRecord> m_records;
    QVector<ProcedurePath> m_procedures;
    
    // Helper methods
    void buildProcedurePaths();
    QString recordTypeToString(RecordType type) const;
};

// Helper function
RecordType parseRecordType(const QString& sectionCode);
