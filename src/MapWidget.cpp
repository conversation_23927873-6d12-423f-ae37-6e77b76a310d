#include "MapWidget.h"
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>

MapWidget::MapWidget(QWidget* parent) 
    : QWidget(parent), m_mapReady(false) {
    setupUI();
}

void MapWidget::setupUI() {
    m_layout = new QVBoxLayout(this);
    m_webView = new QWebEngineView(this);
    m_layout->addWidget(m_webView);
    setLayout(m_layout);
    
    connect(m_webView, &QWebEngineView::loadFinished, this, &MapWidget::onMapReady);
}

void MapWidget::initializeMap() {
    QString html = generateMapHTML();
    m_webView->setHtml(html);
}

void MapWidget::onMapReady() {
    m_mapReady = true;
    qDebug() << "Map is ready";
    
    // Plot any pending data
    if (!m_currentRecords.isEmpty()) {
        plotRecords(m_currentRecords);
    }
    if (!m_currentProcedures.isEmpty()) {
        plotProcedures(m_currentProcedures);
    }
}

QString MapWidget::generateMapHTML() {
    return R"(
<!DOCTYPE html>
<html>
<head>
    <title>ARINC 424 Map</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <style>
        body { margin: 0; padding: 0; }
        #map { height: 100vh; width: 100%; }
    </style>
</head>
<body>
    <div id="map"></div>
    <script>
        var map = L.map('map').setView([45.0, -100.0], 4);
        
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);
        
        var recordsLayer = L.layerGroup().addTo(map);
        var proceduresLayer = L.layerGroup().addTo(map);
        
        function clearRecords() {
            recordsLayer.clearLayers();
        }
        
        function clearProcedures() {
            proceduresLayer.clearLayers();
        }
        
        function addRecord(lat, lng, name, type, color, icon) {
            var marker = L.circleMarker([lat, lng], {
                radius: 6,
                fillColor: color,
                color: '#000',
                weight: 1,
                opacity: 1,
                fillOpacity: 0.8
            });
            
            marker.bindPopup('<b>' + name + '</b><br>Type: ' + type + '<br>Lat: ' + lat.toFixed(6) + '<br>Lng: ' + lng.toFixed(6));
            recordsLayer.addLayer(marker);
        }
        
        function addProcedure(waypoints, name, color) {
            if (waypoints.length < 2) return;
            
            var latlngs = waypoints.map(function(wp) {
                return [wp.lat, wp.lng];
            });
            
            var polyline = L.polyline(latlngs, {
                color: color,
                weight: 3,
                opacity: 0.8
            });
            
            polyline.bindPopup('<b>' + name + '</b><br>Waypoints: ' + waypoints.length);
            proceduresLayer.addLayer(polyline);
            
            // Add waypoint markers
            waypoints.forEach(function(wp, index) {
                var marker = L.circleMarker([wp.lat, wp.lng], {
                    radius: 4,
                    fillColor: color,
                    color: '#000',
                    weight: 1,
                    opacity: 1,
                    fillOpacity: 1
                });
                
                marker.bindPopup('<b>' + wp.name + '</b><br>Sequence: ' + (index + 1));
                proceduresLayer.addLayer(marker);
            });
        }
        
        function setMapCenter(lat, lng, zoom) {
            map.setView([lat, lng], zoom);
        }
        
        // Make functions available to Qt
        window.clearRecords = clearRecords;
        window.clearProcedures = clearProcedures;
        window.addRecord = addRecord;
        window.addProcedure = addProcedure;
        window.setMapCenter = setMapCenter;
    </script>
</body>
</html>
)";
}

void MapWidget::clearMap() {
    if (!m_mapReady) return;
    
    m_webView->page()->runJavaScript("clearRecords();");
    m_webView->page()->runJavaScript("clearProcedures();");
}

void MapWidget::plotRecords(const QVector<ArincRecord>& records) {
    m_currentRecords = records;
    
    if (!m_mapReady) return;
    
    clearMap();
    
    for (const auto& record : records) {
        if (!record.coordinate.isValid()) continue;
        
        QString color = getRecordColor(record.type);
        QString icon = getRecordIcon(record.type);
        QString name = record.getDisplayName();
        QString type = QString::number(static_cast<int>(record.type));
        
        QString js = QString("addRecord(%1, %2, '%3', '%4', '%5', '%6');")
                    .arg(record.coordinate.latitude())
                    .arg(record.coordinate.longitude())
                    .arg(name.replace("'", "\\'"))
                    .arg(type)
                    .arg(color)
                    .arg(icon);
        
        m_webView->page()->runJavaScript(js);
    }
    
    // Auto-fit map to show all records
    if (!records.isEmpty()) {
        double minLat = 90, maxLat = -90, minLng = 180, maxLng = -180;
        for (const auto& record : records) {
            if (record.coordinate.isValid()) {
                minLat = qMin(minLat, record.coordinate.latitude());
                maxLat = qMax(maxLat, record.coordinate.latitude());
                minLng = qMin(minLng, record.coordinate.longitude());
                maxLng = qMax(maxLng, record.coordinate.longitude());
            }
        }
        
        double centerLat = (minLat + maxLat) / 2;
        double centerLng = (minLng + maxLng) / 2;
        setMapCenter(QGeoCoordinate(centerLat, centerLng), 6);
    }
}

void MapWidget::plotProcedures(const QVector<ProcedurePath>& procedures) {
    m_currentProcedures = procedures;
    
    if (!m_mapReady) return;
    
    for (const auto& procedure : procedures) {
        if (!procedure.isValid()) continue;
        
        QString color = getRecordColor(procedure.procedureType);
        QString name = procedure.procedureId;
        
        // Build waypoints array for JavaScript
        QStringList waypointStrings;
        for (const auto& waypoint : procedure.waypoints) {
            if (waypoint.coordinate.isValid()) {
                QString wpStr = QString("{lat: %1, lng: %2, name: '%3'}")
                               .arg(waypoint.coordinate.latitude())
                               .arg(waypoint.coordinate.longitude())
                               .arg(waypoint.getDisplayName().replace("'", "\\'"));
                waypointStrings.append(wpStr);
            }
        }
        
        if (waypointStrings.size() >= 2) {
            QString js = QString("addProcedure([%1], '%2', '%3');")
                        .arg(waypointStrings.join(", "))
                        .arg(name.replace("'", "\\'"))
                        .arg(color);
            
            m_webView->page()->runJavaScript(js);
        }
    }
}

void MapWidget::setMapCenter(const QGeoCoordinate& center, int zoom) {
    if (!m_mapReady || !center.isValid()) return;
    
    QString js = QString("setMapCenter(%1, %2, %3);")
                .arg(center.latitude())
                .arg(center.longitude())
                .arg(zoom);
    
    m_webView->page()->runJavaScript(js);
}

void MapWidget::onRecordsUpdated(const QVector<ArincRecord>& records) {
    plotRecords(records);
}

void MapWidget::onProceduresUpdated(const QVector<ProcedurePath>& procedures) {
    plotProcedures(procedures);
}

QString MapWidget::getRecordColor(RecordType type) {
    switch (type) {
        case RecordType::VHF_NAVAID: return "#FF6B6B";
        case RecordType::NDB_NAVAID: return "#4ECDC4";
        case RecordType::WAYPOINT: return "#45B7D1";
        case RecordType::AIRWAYS_MARKER: return "#96CEB4";
        case RecordType::HOLDING_PATTERN: return "#FFEAA7";
        case RecordType::ENROUTE_AIRWAYS: return "#DDA0DD";
        case RecordType::PREFERRED_ROUTE: return "#98D8C8";
        case RecordType::AIRPORT: return "#F7DC6F";
        case RecordType::AIRPORT_NDB: return "#BB8FCE";
        case RecordType::CONTROLLED_AIRSPACE: return "#85C1E9";
        case RecordType::RESTRICTIVE_AIRSPACE: return "#F8C471";
        default: return "#BDC3C7";
    }
}

QString MapWidget::getRecordIcon(RecordType type) {
    // For future use with custom icons
    return "circle";
}
