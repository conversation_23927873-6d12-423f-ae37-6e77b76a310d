#include "DataModels.h"
#include <QDebug>
#include <QRegularExpression>
#include <algorithm>

ArincDataModel::ArincDataModel() {
}

void ArincDataModel::addRecord(const ArincRecord& record) {
    if (record.isValid()) {
        m_records.append(record);

        // If this is a procedure record, trigger procedure path building
        if (record.type == RecordType::SID || record.type == RecordType::STAR || record.type == RecordType::APPROACH) {
            // We'll rebuild all procedures when data loading is complete
            // For now, just add the record
        }
    }
}

void ArincDataModel::clearData() {
    m_records.clear();
    m_procedures.clear();
}

void ArincDataModel::finalizeProcedures() {
    buildProcedurePaths();
}

QVector<ArincRecord> ArincDataModel::getFilteredRecords(
    const QStringList& regions,
    const QStringList& recordTypes,
    const QStringList& airports) const {
    
    QVector<ArincRecord> filtered;
    
    for (const auto& record : m_records) {
        // Filter by region
        if (!regions.isEmpty() && !regions.contains(record.customerAreaCode)) {
            continue;
        }
        
        // Filter by record type
        if (!recordTypes.isEmpty()) {
            QString typeStr = recordTypeToString(record.type);
            if (!recordTypes.contains(typeStr)) {
                continue;
            }
        }
        
        // Filter by airport (check if identifier contains airport code)
        if (!airports.isEmpty()) {
            bool matchesAirport = false;
            for (const QString& airport : airports) {
                if (record.identifier.contains(airport, Qt::CaseInsensitive) ||
                    record.additionalData.value("Airport Identifier").toString().contains(airport, Qt::CaseInsensitive)) {
                    matchesAirport = true;
                    break;
                }
            }
            if (!matchesAirport) {
                continue;
            }
        }
        
        filtered.append(record);
    }
    
    return filtered;
}

QVector<ProcedurePath> ArincDataModel::getFilteredProcedures(
    const QStringList& airports,
    const QStringList& procedureTypes) const {
    
    QVector<ProcedurePath> filtered;
    
    for (const auto& procedure : m_procedures) {
        // Filter by airport
        if (!airports.isEmpty() && !airports.contains(procedure.airportCode, Qt::CaseInsensitive)) {
            continue;
        }
        
        // Filter by procedure type
        if (!procedureTypes.isEmpty()) {
            QString typeStr = recordTypeToString(procedure.procedureType);
            if (!procedureTypes.contains(typeStr)) {
                continue;
            }
        }
        
        filtered.append(procedure);
    }
    
    return filtered;
}

QStringList ArincDataModel::getAvailableRegions() const {
    QStringList regions;
    for (const auto& record : m_records) {
        if (!regions.contains(record.customerAreaCode)) {
            regions.append(record.customerAreaCode);
        }
    }
    regions.sort();
    return regions;
}

QStringList ArincDataModel::getAvailableRecordTypes(const QStringList& selectedAirports) const {
    QStringList types;
    for (const auto& record : m_records) {
        // Filter by airport if specified
        if (!selectedAirports.isEmpty()) {
            QString airportId = record.additionalData.value("Airport Identifier").toString();
            if (!selectedAirports.contains(airportId)) {
                continue;
            }
        }

        QString typeStr = recordTypeToString(record.type);
        if (!types.contains(typeStr)) {
            types.append(typeStr);
        }
    }
    types.sort();
    return types;
}

QStringList ArincDataModel::getAvailableAirports(const QStringList& selectedRegions) const {
    QStringList airports;
    for (const auto& record : m_records) {
        // Filter by region if specified
        if (!selectedRegions.isEmpty() && !selectedRegions.contains(record.customerAreaCode)) {
            continue;
        }

        QString airportId = record.additionalData.value("Airport Identifier").toString();
        if (!airportId.isEmpty() && !airports.contains(airportId)) {
            airports.append(airportId);
        }
    }
    airports.sort();
    return airports;
}

int ArincDataModel::getRecordCount(RecordType type) const {
    return std::count_if(m_records.begin(), m_records.end(),
                        [type](const ArincRecord& record) {
                            return record.type == type;
                        });
}

RecordType parseRecordType(const QString& sectionCode) {
    if (sectionCode == "D ") return RecordType::VHF_NAVAID;
    if (sectionCode == "DB") return RecordType::NDB_NAVAID;
    if (sectionCode == "EA") return RecordType::WAYPOINT;
    if (sectionCode == "EM") return RecordType::AIRWAYS_MARKER;
    if (sectionCode == "EP") return RecordType::HOLDING_PATTERN;
    if (sectionCode == "ER") return RecordType::ENROUTE_AIRWAYS;
    if (sectionCode == "ET") return RecordType::PREFERRED_ROUTE;
    if (sectionCode == "PA") return RecordType::AIRPORT;
    if (sectionCode == "PD") return RecordType::SID;
    if (sectionCode == "PE") return RecordType::STAR;
    if (sectionCode == "PF") return RecordType::APPROACH;
    if (sectionCode == "PN") return RecordType::AIRPORT_NDB;
    if (sectionCode == "UC") return RecordType::CONTROLLED_AIRSPACE;
    if (sectionCode == "UR") return RecordType::RESTRICTIVE_AIRSPACE;
    return RecordType::UNKNOWN;
}

QString ArincDataModel::recordTypeToString(RecordType type) const {
    switch (type) {
        case RecordType::VHF_NAVAID: return "VHF Navaid";
        case RecordType::NDB_NAVAID: return "NDB Navaid";
        case RecordType::WAYPOINT: return "Waypoint";
        case RecordType::AIRWAYS_MARKER: return "Airways Marker";
        case RecordType::HOLDING_PATTERN: return "Holding Pattern";
        case RecordType::ENROUTE_AIRWAYS: return "Enroute Airways";
        case RecordType::PREFERRED_ROUTE: return "Preferred Route";
        case RecordType::AIRPORT: return "Airport";
        case RecordType::AIRPORT_NDB: return "Airport NDB";
        case RecordType::SID: return "SID";
        case RecordType::STAR: return "STAR";
        case RecordType::APPROACH: return "Approach";
        case RecordType::CONTROLLED_AIRSPACE: return "Controlled Airspace";
        case RecordType::RESTRICTIVE_AIRSPACE: return "Restrictive Airspace";
        default: return "Unknown";
    }
}

void ArincDataModel::buildProcedurePaths() {
    m_procedures.clear();

    // Group procedure records by airport, procedure ID, and transition
    QMap<QString, QVector<ArincRecord>> procedureGroups;

    for (const auto& record : m_records) {
        if (record.type == RecordType::SID || record.type == RecordType::STAR || record.type == RecordType::APPROACH) {
            QString airportId = record.additionalData.value("Airport Identifier").toString();
            QString procedureId = record.procedureIdentifier;
            QString transitionId = record.additionalData.value("Transition Identifier").toString();

            if (!airportId.isEmpty() && !procedureId.isEmpty()) {
                QString groupKey = QString("%1_%2_%3").arg(airportId, procedureId, transitionId);
                procedureGroups[groupKey].append(record);
            }
        }
    }

    // Build procedure paths from grouped records
    for (auto it = procedureGroups.begin(); it != procedureGroups.end(); ++it) {
        QVector<ArincRecord> records = it.value();

        if (records.size() < 2) continue; // Need at least 2 waypoints for a path

        // Sort by sequence number
        std::sort(records.begin(), records.end(), [](const ArincRecord& a, const ArincRecord& b) {
            return a.sequenceNumber < b.sequenceNumber;
        });

        // Create procedure path
        ProcedurePath procedure;
        procedure.procedureId = records.first().procedureIdentifier;
        procedure.airportCode = records.first().additionalData.value("Airport Identifier").toString();
        procedure.procedureType = records.first().type;
        procedure.waypoints = records;

        if (procedure.isValid()) {
            m_procedures.append(procedure);
        }
    }
}
