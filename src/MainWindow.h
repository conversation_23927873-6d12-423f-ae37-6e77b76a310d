#pragma once

#include <QMainWindow>
#include <QMenuBar>
#include <QStatusBar>
#include <QSplitter>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QWidget>
#include <QPushButton>
#include <QFileDialog>
#include <QProgressBar>
#include <QLabel>
#include <QTimer>

#include "DataModels.h"
#include "PythonInterface.h"
#include "MapWidget.h"
#include "FilterWidget.h"

class MainWindow : public QMainWindow {
    Q_OBJECT

public:
    MainWindow(QWidget* parent = nullptr);
    ~MainWindow();

private slots:
    void openFile();
    void onFiltersChanged();
    void updateMapDisplay();
    void showAbout();

private:
    void setupUI();
    void setupMenuBar();
    void setupStatusBar();
    void connectSignals();
    void updateFilterOptions();
    void updateStatusBar();
    
    // UI Components
    QWidget* m_centralWidget;
    QSplitter* m_mainSplitter;
    QWidget* m_leftPanel;
    QVBoxLayout* m_leftLayout;
    
    QPushButton* m_openFileBtn;
    FilterWidget* m_filterWidget;
    MapWidget* m_mapWidget;
    
    QProgressBar* m_progressBar;
    QLabel* m_statusLabel;
    QLabel* m_recordCountLabel;
    
    // Data and logic
    ArincDataModel* m_dataModel;
    PythonInterface* m_pythonInterface;
    
    QString m_currentFilePath;
    QTimer* m_updateTimer;
};
