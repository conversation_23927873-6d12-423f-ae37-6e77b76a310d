#pragma once

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGroupBox>
#include <QCheckBox>
#include <QListWidget>
#include <QPushButton>
#include <QLabel>
#include <QStringList>

class FilterWidget : public QWidget {
    Q_OBJECT

public:
    explicit FilterWidget(QWidget* parent = nullptr);
    
    void setAvailableRegions(const QStringList& regions);
    void setAvailableRecordTypes(const QStringList& types);
    void setAvailableAirports(const QStringList& airports);
    
    QStringList getSelectedRegions() const;
    QStringList getSelectedRecordTypes() const;
    QStringList getSelectedAirports() const;
    
    void clearSelections();

signals:
    void filtersChanged();

private slots:
    void onRegionSelectionChanged();
    void onRecordTypeSelectionChanged();
    void onAirportSelectionChanged();
    void onSelectAllRegions();
    void onClearAllRegions();
    void onSelectAllTypes();
    void onClearAllTypes();
    void onSelectAllAirports();
    void onClearAllAirports();

private:
    void setupUI();
    void setupRegionFilter();
    void setupRecordTypeFilter();
    void setupAirportFilter();
    
    QVBoxLayout* m_mainLayout;
    
    // Region filter
    QGroupBox* m_regionGroup;
    QListWidget* m_regionList;
    QPushButton* m_selectAllRegionsBtn;
    QPushButton* m_clearAllRegionsBtn;
    
    // Record type filter
    QGroupBox* m_recordTypeGroup;
    QListWidget* m_recordTypeList;
    QPushButton* m_selectAllTypesBtn;
    QPushButton* m_clearAllTypesBtn;
    
    // Airport filter
    QGroupBox* m_airportGroup;
    QListWidget* m_airportList;
    QPushButton* m_selectAllAirportsBtn;
    QPushButton* m_clearAllAirportsBtn;
    
    // Statistics
    QLabel* m_statsLabel;
};
