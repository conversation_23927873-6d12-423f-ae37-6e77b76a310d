cmake_minimum_required(VERSION 3.16)
project(ARINC424Viewer)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(Qt5 REQUIRED COMPONENTS Core Widgets WebEngineWidgets)
find_package(PkgConfig REQUIRED)
pkg_check_modules(PYTHON REQUIRED python3-embed)

# Add executable
add_executable(arinc424_viewer
    src/main.cpp
    src/MainWindow.cpp
    src/MainWindow.h
    src/PythonInterface.cpp
    src/PythonInterface.h
    src/DataModels.cpp
    src/DataModels.h
    src/MapWidget.cpp
    src/MapWidget.h
    src/FilterWidget.cpp
    src/FilterWidget.h
)

# Link libraries
target_link_libraries(arinc424_viewer
    Qt5::Core
    Qt5::Widgets
    Qt5::WebEngineWidgets
    ${PYTHON_LIBRARIES}
)

# Include directories
target_include_directories(arinc424_viewer PRIVATE 
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${PYTHON_INCLUDE_DIRS}
)

# Compiler flags
target_compile_options(arinc424_viewer PRIVATE ${PYTHON_CFLAGS_OTHER})

# Copy Python scripts to build directory
configure_file(${CMAKE_CURRENT_SOURCE_DIR}/python/arinc_parser.py 
               ${CMAKE_CURRENT_BINARY_DIR}/python/arinc_parser.py COPYONLY)

# Set up Qt MOC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)
